"""Synchronous adapter for async repositories used in performance tests.

This module provides synchronous wrappers around async repositories to enable
their use in performance tests that require synchronous execution patterns,
particularly for threading and multiprocessing scenarios.
"""

import asyncio
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.models.general.user import User
from src.core.repositories.general.user_repository import UserRepository
from src.core.models.general.component import Component
from src.core.repositories.general.component_repository import ComponentRepository
from src.core.utils.pagination_utils import PaginationParams, PaginationResult


class SyncUserRepositoryAdapter:
    """Synchronous adapter for UserRepository.

    This adapter wraps the async UserRepository methods and executes them
    synchronously using asyncio.run(). Each method creates a fresh async
    session to avoid event loop conflicts.
    """

    def __init__(self, sync_session: Session):
        """Initialize the adapter with a synchronous session.

        Note: All operations now use the sync_session directly to avoid
        memory leaks and session isolation issues.
        """
        self.sync_session = sync_session

    def create(self, user_data: dict) -> User:
        """Create a user synchronously using the same sync session."""
        from src.core.models.general.user import User

        # Use the sync session directly to avoid memory leaks
        user = User(**user_data)
        self.sync_session.add(user)
        self.sync_session.flush()  # Get the ID
        return user

    def get_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID synchronously using the same sync session."""
        from src.core.models.general.user import User

        # Use the sync session directly to avoid memory leaks
        user = self.sync_session.query(User).filter(User.id == user_id, User.is_active == True).first()
        return user

    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email synchronously using the same sync session."""
        from sqlalchemy import and_, func
        from src.core.models.general.user import User

        # Use the sync session directly to avoid session isolation issues
        normalized_email = email.lower().strip()
        user = (
            self.sync_session.query(User)
            .filter(
                and_(
                    func.lower(User.email) == normalized_email,
                    User.is_active == True,
                )
            )
            .first()
        )
        return user

    def check_email_exists(self, email: str, exclude_user_id: Optional[int] = None) -> bool:
        """Check if email exists synchronously using the same sync session."""
        from sqlalchemy import and_, func
        from src.core.models.general.user import User

        # Use the sync session directly to avoid session isolation issues
        normalized_email = email.lower().strip()
        conditions = [func.lower(User.email) == normalized_email]
        if exclude_user_id is not None:
            conditions.append(User.id != exclude_user_id)

        count = self.sync_session.query(func.count(User.id)).filter(and_(*conditions)).scalar()
        return count > 0

    def get_by_name(self, name: str) -> Optional[User]:
        """Get user by name synchronously using the same sync session."""
        from src.core.models.general.user import User

        # Use the sync session directly to avoid memory leaks
        user = self.sync_session.query(User).filter(User.name == name, User.is_active == True).first()
        return user

    def update(self, user_id: int, update_data: dict) -> Optional[User]:
        """Update user synchronously using the same sync session."""
        from src.core.models.general.user import User

        # Use the sync session directly to avoid memory leaks
        user = self.sync_session.query(User).filter(User.id == user_id).first()
        if user:
            for key, value in update_data.items():
                setattr(user, key, value)
            self.sync_session.flush()
        return user

    def delete(self, user_id: int) -> bool:
        """Delete user synchronously using the same sync session."""
        from src.core.models.general.user import User

        # Use the sync session directly to avoid memory leaks
        user = self.sync_session.query(User).filter(User.id == user_id).first()
        if user:
            user.is_active = False  # Soft delete
            self.sync_session.flush()
            return True
        return False

    def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get active users synchronously using the same sync session."""
        from src.core.models.general.user import User

        # Use the sync session directly to avoid memory leaks
        users = self.sync_session.query(User).filter(User.is_active == True).offset(skip).limit(limit).all()
        return users

    def search_users(self, search_term: str, skip: int = 0, limit: int = 100) -> List[User]:
        """Search users synchronously using the same sync session."""
        from sqlalchemy import or_
        from src.core.models.general.user import User

        # Use the sync session directly to avoid memory leaks
        users = (
            self.sync_session.query(User)
            .filter(
                User.is_active == True, or_(User.name.ilike(f"%{search_term}%"), User.email.ilike(f"%{search_term}%"))
            )
            .offset(skip)
            .limit(limit)
            .all()
        )
        return users

    def count_active_users(self) -> int:
        """Count active users synchronously using the same sync session."""
        from sqlalchemy import func
        from src.core.models.general.user import User

        # Use the sync session directly to avoid memory leaks
        count = self.sync_session.query(func.count(User.id)).filter(User.is_active == True).scalar()
        return count

    def close(self):
        """Close method for compatibility. No persistent session to close."""
        pass

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()


class SyncComponentRepositoryAdapter:
    """Synchronous adapter for ComponentRepository.

    This adapter wraps the async ComponentRepository methods and executes them
    synchronously using asyncio.run(). Each method creates a fresh async
    session to avoid event loop conflicts.
    """

    def __init__(self, sync_session: Session):
        """Initialize the adapter with a synchronous session."""
        self.sync_session = sync_session

    async def _create_async_session(self):
        """Create a fresh async session with isolated engine for each operation."""
        from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
        from src.config.settings import settings

        # Use the same database URL from settings to ensure consistency
        db_url = settings.DATABASE_URL

        # Convert to async URL
        if "postgresql" in db_url and "asyncpg" not in db_url:
            async_url = db_url.replace("postgresql://", "postgresql+asyncpg://")
        else:
            async_url = db_url

        # Create isolated async engine with minimal pool settings for performance tests
        isolated_engine = create_async_engine(
            async_url,
            pool_size=1,
            max_overflow=0,
            pool_pre_ping=True,
            echo=False,
        )

        # Create session
        async_session_maker = async_sessionmaker(isolated_engine, class_=AsyncSession, expire_on_commit=False)
        async_session = async_session_maker()

        return async_session, isolated_engine

    def get_by_id(self, component_id: int) -> Optional[Component]:
        """Get component by ID synchronously."""

        async def _get_by_id():
            async_session, isolated_engine = await self._create_async_session()
            try:
                component_repo = ComponentRepository(async_session)
                return await component_repo.get_by_id(component_id)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_get_by_id())

    def get_by_type_id(self, component_type_id: int, skip: int = 0, limit: int = 100) -> List[Component]:
        """Get components by type ID synchronously."""

        async def _get_by_type_id():
            async_session, isolated_engine = await self._create_async_session()
            try:
                component_repo = ComponentRepository(async_session)
                return await component_repo.get_by_type_id(component_type_id, skip, limit)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_get_by_type_id())

    def search_components(self, search_term: str, skip: int = 0, limit: int = 100) -> List[Component]:
        """Search components synchronously."""

        async def _search_components():
            async_session, isolated_engine = await self._create_async_session()
            try:
                component_repo = ComponentRepository(async_session)
                return await component_repo.search_components(search_term, skip, limit)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_search_components())

    def get_preferred_components(self, skip: int = 0, limit: int = 100) -> List[Component]:
        """Get preferred components synchronously."""

        async def _get_preferred_components():
            async_session, isolated_engine = await self._create_async_session()
            try:
                component_repo = ComponentRepository(async_session)
                return await component_repo.get_preferred_components(skip, limit)
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_get_preferred_components())

    def get_components_paginated_with_filters(
        self,
        pagination_params: PaginationParams,
        category_id: Optional[int] = None,
        component_type_id: Optional[int] = None,
        manufacturer: Optional[str] = None,
        is_preferred: Optional[bool] = None,
        search_term: Optional[str] = None,
    ) -> PaginationResult:
        """Get paginated components with filters synchronously."""

        async def _get_components_paginated_with_filters():
            async_session, isolated_engine = await self._create_async_session()
            try:
                component_repo = ComponentRepository(async_session)
                return await component_repo.get_components_paginated_with_filters(
                    pagination_params, category_id, component_type_id, manufacturer, is_preferred, search_term
                )
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_get_components_paginated_with_filters())

    def count_active_components(self) -> int:
        """Count active components synchronously."""

        async def _count_active_components():
            async_session, isolated_engine = await self._create_async_session()
            try:
                component_repo = ComponentRepository(async_session)
                return await component_repo.count_active_components()
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_count_active_components())

    def count_components_by_category_id(self) -> dict:
        """Count components by category ID synchronously."""

        async def _count_components_by_category_id():
            async_session, isolated_engine = await self._create_async_session()
            try:
                component_repo = ComponentRepository(async_session)
                return await component_repo.count_components_by_category_id()
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_count_components_by_category_id())

    def create(self, component_data: dict) -> Component:
        """Create component synchronously."""

        async def _create():
            async_session, isolated_engine = await self._create_async_session()
            try:
                component_repo = ComponentRepository(async_session)
                component = await component_repo.create(component_data)
                await async_session.commit()
                return component
            finally:
                await async_session.close()
                await isolated_engine.dispose()

        return asyncio.run(_create())

    def close(self):
        """Close method for compatibility. No persistent session to close."""
        pass

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
